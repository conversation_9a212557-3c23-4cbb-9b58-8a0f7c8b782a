{"name": "cloudflare-workers-openapi", "version": "0.0.1", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types"}, "dependencies": {"@hono/zod-validator": "^0.7.2", "bcryptjs": "^3.0.2", "chanfana": "^2.6.3", "hono": "^4.6.20", "nanoid": "^5.1.5", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "22.13.0", "@types/service-worker-mock": "^2.0.4", "wrangler": "^4.26.1"}}