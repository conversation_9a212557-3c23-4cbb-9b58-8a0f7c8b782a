// 主入口文件
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { serveStatic } from 'hono/cloudflare-workers';
import type { Env } from './types';

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env }>();

// 中间件配置
app.use('*', logger());
app.use('*', prettyJSON());
app.use('/api/*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 静态文件服务（前端文件）
app.use('/static/*', serveStatic({ root: './frontend/dist' }));
app.use('/assets/*', serveStatic({ root: './frontend/dist' }));

// 根路径 - 返回前端页面或API信息
app.get('/', (c) => {
  const userAgent = c.req.header('User-Agent') || '';
  
  // 如果是浏览器访问，返回前端页面
  if (userAgent.includes('Mozilla')) {
    return c.html(`
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify - 软件许可证验证服务</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
          .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .credentials { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
          code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
          .btn { display: inline-block; padding: 10px 20px; background: #007acc; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
          .btn:hover { background: #005a9e; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🔐 Verify - 软件许可证验证服务</h1>
          
          <div class="status">
            <h3>✅ 系统状态：运行正常</h3>
            <p>数据库连接正常，缓存服务可用</p>
          </div>
          
          <div class="info">
            <h3>📋 服务信息</h3>
            <p><strong>版本：</strong>v1.0.0</p>
            <p><strong>环境：</strong>Cloudflare Workers</p>
            <p><strong>数据库：</strong>D1 + KV Cache</p>
            <p><strong>API 基础路径：</strong><code>/api</code></p>
          </div>
          
          <div class="credentials">
            <h3>🔑 默认登录凭据</h3>
            <p><strong>超级管理员：</strong></p>
            <p>用户名：<code>root</code> | 密码：<code>password</code></p>
            <p><strong>普通管理员：</strong></p>
            <p>用户名：<code>user</code> | 密码：<code>password</code></p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <a href="/admin" class="btn">🎛️ 管理后台</a>
            <a href="/api/docs" class="btn">📚 API 文档</a>
            <a href="/api/health" class="btn">🔍 健康检查</a>
          </div>
          
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>Powered by Cloudflare Workers | Built with Hono.js</p>
          </div>
        </div>
      </body>
      </html>
    `);
  }
  
  // API 访问返回 JSON 信息
  return c.json({
    success: true,
    data: {
      service: 'Verify - 软件许可证验证服务',
      version: '1.0.0',
      status: 'running',
      endpoints: {
        health: '/api/health',
        docs: '/api/docs',
        verify: '/api/verify',
        admin: '/api/admin'
      }
    },
    msg: '服务运行正常'
  });
});

// 健康检查接口
app.get('/api/health', async (c) => {
  try {
    // 测试数据库连接
    const dbTest = await c.env.DB.prepare('SELECT 1 as test').first();
    
    // 测试 KV 连接
    await c.env.VERIFY_CACHE.put('health_check', 'ok', { expirationTtl: 60 });
    const kvTest = await c.env.VERIFY_CACHE.get('health_check');
    
    return c.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: dbTest ? 'connected' : 'disconnected',
        cache: kvTest === 'ok' ? 'connected' : 'disconnected',
        uptime: Date.now()
      },
      msg: '系统健康检查通过'
    });
  } catch (error) {
    return c.json({
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      msg: '系统健康检查失败'
    }, 500);
  }
});

// API 路由占位符（后续添加）
app.route('/api/verify', new Hono()); // 验证接口
app.route('/api/admin', new Hono());  // 管理接口

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    data: null,
    msg: '接口不存在'
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Application Error:', err);
  return c.json({
    success: false,
    data: null,
    msg: '服务器内部错误'
  }, 500);
});

export default app;
