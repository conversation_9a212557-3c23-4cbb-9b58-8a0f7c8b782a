// 主入口文件
import openapi from './app';
import type { Env } from './types';
import adminRoutes from './routes/admin';

// 添加健康检查接口到 OpenAPI 应用
openapi.get('/api/health', async (c) => {
  try {
    // 测试数据库连接
    const dbTest = await c.env.DB.prepare('SELECT 1 as test').first();

    // 测试 KV 连接
    await c.env.VERIFY_CACHE.put('health_check', 'ok', { expirationTtl: 60 });
    const kvTest = await c.env.VERIFY_CACHE.get('health_check');

    return c.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: dbTest ? 'connected' : 'disconnected',
        cache: kvTest === 'ok' ? 'connected' : 'disconnected',
        uptime: Date.now()
      },
      msg: '系统健康检查通过'
    });
  } catch (error) {
    return c.json({
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      msg: '系统健康检查失败'
    }, 500);
  }
});

// 添加管理员路由
openapi.route('/api/admin', adminRoutes);

// 404 处理
openapi.notFound((c) => {
  return c.json({
    success: false,
    data: null,
    msg: '接口不存在'
  }, 404);
});

// 错误处理
openapi.onError((err, c) => {
  console.error('Application Error:', err);
  return c.json({
    success: false,
    data: null,
    msg: '服务器内部错误'
  }, 500);
});

export default openapi;
