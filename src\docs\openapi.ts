// OpenAPI 文档配置
import { OpenAPIRoute, OpenAPIRouteSchema } from 'chanfana';
import { z } from 'zod';
import type { Env } from '../types';

// 通用响应 Schema
export const ApiResponseSchema = z.object({
  success: z.boolean().describe('请求是否成功'),
  data: z.any().nullable().describe('响应数据，失败时为null'),
  msg: z.string().describe('响应消息')
});

// 管理员登录请求 Schema
export const AdminLoginRequestSchema = z.object({
  username: z.string().min(1).describe('用户名'),
  password: z.string().min(1).describe('密码')
});

// 管理员登录响应 Schema
export const AdminLoginResponseSchema = z.object({
  token: z.string().describe('JWT访问令牌'),
  admin_id: z.string().describe('管理员ID'),
  username: z.string().describe('用户名'),
  role: z.enum(['super', 'normal']).describe('角色：super=超级管理员, normal=普通管理员'),
  authorized_products: z.array(z.string()).optional().describe('授权产品列表（普通管理员）'),
  expires_in: z.number().describe('令牌有效期（秒）')
});

// 许可证验证请求 Schema
export const VerifyRequestSchema = z.object({
  license_key: z.string().min(1).describe('许可证密钥'),
  device_fingerprint: z.string().optional().describe('设备指纹'),
  requested_features: z.array(z.string()).optional().describe('请求的功能列表'),
  client_info: z.object({
    version: z.string().optional().describe('客户端版本'),
    platform: z.string().optional().describe('客户端平台'),
    ip: z.string().optional().describe('客户端IP')
  }).optional().describe('客户端信息')
});

// 许可证验证响应 Schema
export const VerifyResponseSchema = z.object({
  license_id: z.string().describe('许可证ID'),
  product_id: z.string().describe('产品ID'),
  product_name: z.string().describe('产品名称'),
  status: z.string().describe('许可证状态'),
  expiry_date: z.string().nullable().describe('过期时间，null表示永久许可证'),
  days_remaining: z.number().nullable().describe('剩余天数，永久许可证为-1'),
  device_limit: z.number().describe('设备数量限制'),
  current_devices: z.number().describe('当前绑定设备数'),
  device_slots_available: z.number().describe('可用设备槽位'),
  features: z.object({
    granted: z.array(z.string()).describe('已授权功能'),
    denied: z.array(z.string()).describe('被拒绝功能'),
    available: z.array(z.string()).describe('产品所有功能')
  }).describe('功能权限信息'),
  device_info: z.object({
    is_new_device: z.boolean().describe('是否为新设备'),
    device_id: z.string().optional().describe('设备ID'),
    first_seen: z.string().optional().describe('首次绑定时间'),
    last_seen: z.string().optional().describe('最后活跃时间')
  }).optional().describe('设备信息'),
  verification_time: z.string().describe('验证时间')
});

// 健康检查响应 Schema
export const HealthResponseSchema = z.object({
  status: z.string().describe('系统状态'),
  timestamp: z.string().describe('检查时间'),
  database: z.string().describe('数据库连接状态'),
  cache: z.string().describe('缓存连接状态'),
  uptime: z.number().describe('系统运行时间')
});

/**
 * 管理员登录接口文档
 */
export class AdminLogin extends OpenAPIRoute {
  schema: OpenAPIRouteSchema = {
    tags: ['管理员认证'],
    summary: '管理员登录',
    description: '管理员使用用户名和密码登录，获取JWT访问令牌',
    request: {
      body: {
        content: {
          'application/json': {
            schema: AdminLoginRequestSchema
          }
        }
      }
    },
    responses: {
      '200': {
        description: '登录成功',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(true),
              data: AdminLoginResponseSchema,
              msg: z.string()
            })
          }
        }
      },
      '401': {
        description: '用户名或密码错误',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(false),
              data: z.null(),
              msg: z.string()
            })
          }
        }
      }
    }
  };

  async handle(c: any) {
    // 实际的处理逻辑在 routes/admin.ts 中
    return c.json({ msg: 'This endpoint is documented but handled elsewhere' });
  }
}

/**
 * 许可证验证接口文档
 */
export class LicenseVerify extends OpenAPIRoute {
  schema: OpenAPIRouteSchema = {
    tags: ['许可证验证'],
    summary: '许可证验证',
    description: '验证许可证的有效性，支持过期时间、设备限制、功能权限等多种验证策略',
    request: {
      body: {
        content: {
          'application/json': {
            schema: VerifyRequestSchema
          }
        }
      }
    },
    responses: {
      '200': {
        description: '验证成功',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(true),
              data: VerifyResponseSchema,
              msg: z.string()
            })
          }
        }
      },
      '400': {
        description: '验证失败（许可证无效、已过期、设备超限等）',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(false),
              data: z.any().nullable(),
              msg: z.string()
            })
          }
        }
      }
    }
  };

  async handle(c: any) {
    return c.json({ msg: 'This endpoint is documented but handled elsewhere' });
  }
}

/**
 * 健康检查接口文档
 */
export class HealthCheck extends OpenAPIRoute {
  schema: OpenAPIRouteSchema = {
    tags: ['系统监控'],
    summary: '健康检查',
    description: '检查系统运行状态，包括数据库连接、缓存服务等',
    responses: {
      '200': {
        description: '系统健康',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(true),
              data: HealthResponseSchema,
              msg: z.string()
            })
          }
        }
      },
      '500': {
        description: '系统异常',
        content: {
          'application/json': {
            schema: z.object({
              success: z.literal(false),
              data: z.any().nullable(),
              msg: z.string()
            })
          }
        }
      }
    }
  };

  async handle(c: any) {
    return c.json({ msg: 'This endpoint is documented but handled elsewhere' });
  }
}
