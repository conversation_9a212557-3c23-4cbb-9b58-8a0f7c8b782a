// 管理员相关路由
import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import type { Env, ApiResponse, Admin, AdminLoginResponse, JWTPayload } from '../types';
import { verifyPassword, generateJWT, getJWTSecret, verifyJWT } from '../utils/crypto';

const admin = new Hono<{ Bindings: Env }>();

// 登录请求验证schema
const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空')
});

// Token刷新验证schema
const refreshSchema = z.object({
  token: z.string().optional()
});

/**
 * 管理员登录
 * POST /api/admin/login
 */
admin.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { username, password } = c.req.valid('json');
    
    // 查询管理员信息
    const adminQuery = await c.env.DB
      .prepare('SELECT * FROM admins WHERE username = ? AND status = ?')
      .bind(username, 'active')
      .first<Admin>();
    
    if (!adminQuery) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '用户名或密码错误'
      }, 401);
    }
    
    // 验证密码
    const isPasswordValid = await verifyPassword(password, adminQuery.password_hash);
    if (!isPasswordValid) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '用户名或密码错误'
      }, 401);
    }
    
    // 解析授权产品列表
    let authorizedProducts: string[] = [];
    if (adminQuery.authorized_products) {
      try {
        authorizedProducts = JSON.parse(adminQuery.authorized_products);
      } catch (error) {
        console.error('解析授权产品列表失败:', error);
      }
    }
    
    // 生成JWT Token
    const jwtSecret = getJWTSecret(c.env);
    const tokenPayload: JWTPayload = {
      admin_id: adminQuery.id,
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 86400 // 24小时
    };
    
    const token = await generateJWT(tokenPayload, jwtSecret, 86400);
    
    // 更新最后登录时间
    await c.env.DB
      .prepare('UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE id = ?')
      .bind(adminQuery.id)
      .run();
    
    // 缓存管理员权限信息到KV
    const cacheKey = `admin:auth:${adminQuery.id}`;
    const cacheData = {
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      status: adminQuery.status,
      last_updated: new Date().toISOString()
    };
    
    await c.env.VERIFY_CACHE.put(cacheKey, JSON.stringify(cacheData), {
      expirationTtl: 1800 // 30分钟
    });
    
    const response: AdminLoginResponse = {
      token,
      admin_id: adminQuery.id,
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      expires_in: 86400
    };
    
    return c.json<ApiResponse<AdminLoginResponse>>({
      success: true,
      data: response,
      msg: '登录成功'
    });
    
  } catch (error) {
    console.error('登录错误:', error);
    return c.json<ApiResponse>({
      success: false,
      data: null,
      msg: '登录失败，请稍后重试'
    }, 500);
  }
});

/**
 * Token刷新
 * POST /api/admin/refresh
 */
admin.post('/refresh', async (c) => {
  try {
    // 从Authorization头获取token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '缺少认证令牌'
      }, 401);
    }
    
    const token = authHeader.substring(7);
    const jwtSecret = getJWTSecret(c.env);
    
    // 验证当前token
    let payload: JWTPayload;
    try {
      payload = await verifyJWT(token, jwtSecret);
    } catch (error) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: 'Token无效或已过期'
      }, 401);
    }
    
    // 检查管理员是否仍然有效
    const adminQuery = await c.env.DB
      .prepare('SELECT * FROM admins WHERE id = ? AND status = ?')
      .bind(payload.admin_id, 'active')
      .first<Admin>();
    
    if (!adminQuery) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '用户账号已被禁用'
      }, 401);
    }
    
    // 生成新的token
    const newTokenPayload: JWTPayload = {
      admin_id: payload.admin_id,
      username: payload.username,
      role: payload.role,
      authorized_products: payload.authorized_products,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 86400
    };
    
    const newToken = await generateJWT(newTokenPayload, jwtSecret, 86400);
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        token: newToken,
        expires_in: 86400
      },
      msg: 'Token刷新成功'
    });
    
  } catch (error) {
    console.error('Token刷新错误:', error);
    return c.json<ApiResponse>({
      success: false,
      data: null,
      msg: 'Token刷新失败'
    }, 500);
  }
});

/**
 * 获取当前用户信息
 * GET /api/admin/me
 */
admin.get('/me', async (c) => {
  try {
    // 从Authorization头获取token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '缺少认证令牌'
      }, 401);
    }
    
    const token = authHeader.substring(7);
    const jwtSecret = getJWTSecret(c.env);
    
    // 验证token
    let payload: JWTPayload;
    try {
      payload = await verifyJWT(token, jwtSecret);
    } catch (error) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: 'Token无效或已过期'
      }, 401);
    }
    
    // 获取管理员详细信息
    const adminQuery = await c.env.DB
      .prepare('SELECT id, username, role, authorized_products, status, created_at, last_login FROM admins WHERE id = ?')
      .bind(payload.admin_id)
      .first();
    
    if (!adminQuery) {
      return c.json<ApiResponse>({
        success: false,
        data: null,
        msg: '用户不存在'
      }, 404);
    }
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        admin_id: adminQuery.id,
        username: adminQuery.username,
        role: adminQuery.role,
        authorized_products: adminQuery.authorized_products ? JSON.parse(adminQuery.authorized_products as string) : [],
        status: adminQuery.status,
        created_at: adminQuery.created_at,
        last_login: adminQuery.last_login
      },
      msg: '获取用户信息成功'
    });
    
  } catch (error) {
    console.error('获取用户信息错误:', error);
    return c.json<ApiResponse>({
      success: false,
      data: null,
      msg: '获取用户信息失败'
    }, 500);
  }
});

export default admin;
