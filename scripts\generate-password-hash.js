// 生成密码哈希的脚本
const bcrypt = require('bcryptjs');

async function generateHashes() {
  console.log('生成密码哈希...\n');
  
  // 超级管理员密码: admin123
  const adminHash = await bcrypt.hash('admin123', 10);
  console.log('超级管理员 (admin) 密码: admin123');
  console.log('哈希值:', adminHash);
  console.log('');
  
  // 普通管理员密码: distributor123
  const distributorHash = await bcrypt.hash('distributor123', 10);
  console.log('普通管理员 (distributor01) 密码: distributor123');
  console.log('哈希值:', distributorHash);
  console.log('');
  
  // 验证哈希是否正确
  const adminVerify = await bcrypt.compare('admin123', adminHash);
  const distributorVerify = await bcrypt.compare('distributor123', distributorHash);
  
  console.log('验证结果:');
  console.log('admin123 验证:', adminVerify ? '✓ 正确' : '✗ 错误');
  console.log('distributor123 验证:', distributorVerify ? '✓ 正确' : '✗ 错误');
}

generateHashes().catch(console.error);
