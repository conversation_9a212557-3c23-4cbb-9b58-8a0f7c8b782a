// 生成密码哈希的脚本
const bcrypt = require('bcryptjs');

async function generateHashes() {
  console.log('生成密码哈希...\n');

  // 超级管理员密码: root
  const rootHash = await bcrypt.hash('root', 10);
  console.log('超级管理员 (root) 密码: root');
  console.log('哈希值:', rootHash);
  console.log('');

  // 普通管理员密码: user
  const userHash = await bcrypt.hash('user', 10);
  console.log('普通管理员 (user) 密码: user');
  console.log('哈希值:', userHash);
  console.log('');

  // 验证哈希是否正确
  const rootVerify = await bcrypt.compare('root', rootHash);
  const userVerify = await bcrypt.compare('user', userHash);

  console.log('验证结果:');
  console.log('root 验证:', rootVerify ? '✓ 正确' : '✗ 错误');
  console.log('user 验证:', userVerify ? '✓ 正确' : '✗ 错误');
}

generateHashes().catch(console.error);
